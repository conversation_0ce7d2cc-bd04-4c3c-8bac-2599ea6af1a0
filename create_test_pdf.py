#!/usr/bin/env python3
"""
Simple script to create a test PDF file for the annotation app
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
import os

def create_test_pdf():
    filename = "test_document.pdf"
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Page 1
    c.setFont("Helvetica-Bold", 24)
    c.drawString(100, height - 100, "Test Document for Annotation")
    
    c.setFont("Helvetica", 12)
    c.drawString(100, height - 150, "This is a test PDF document created for testing the annotation app.")
    c.drawString(100, height - 170, "You can draw rectangles and polygons on this document.")
    c.drawString(100, height - 190, "The coordinates will be preserved relative to the PDF dimensions.")
    
    # Draw some shapes for reference
    c.setStrokeColorRGB(0.5, 0.5, 0.5)
    c.rect(100, height - 300, 200, 100, fill=0)
    c.drawString(110, height - 250, "Reference Rectangle")
    
    c.circle(400, height - 250, 50, fill=0)
    c.drawString(370, height - 320, "Reference Circle")
    
    c.showPage()
    
    # Page 2
    c.setFont("Helvetica-Bold", 20)
    c.drawString(100, height - 100, "Second Page")
    
    c.setFont("Helvetica", 12)
    c.drawString(100, height - 150, "This is the second page of the test document.")
    c.drawString(100, height - 170, "You can navigate between pages and add annotations to each.")
    
    # Add a grid for easier annotation testing
    c.setStrokeColorRGB(0.8, 0.8, 0.8)
    for i in range(0, int(width), 50):
        c.line(i, 0, i, height)
    for i in range(0, int(height), 50):
        c.line(0, i, width, i)
    
    c.setStrokeColorRGB(0, 0, 0)
    c.drawString(100, height - 200, "Grid lines for easier annotation positioning")
    
    c.save()
    print(f"Created {filename}")

if __name__ == "__main__":
    try:
        create_test_pdf()
    except ImportError:
        print("reportlab not installed. Installing...")
        os.system("pip install reportlab")
        create_test_pdf()
