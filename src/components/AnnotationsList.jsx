import React, { useState } from 'react'

const AnnotationsList = ({
  annotations,
  selectedAnnotation,
  onSelectAnnotation,
  onCopyAnnotation,
  onDeleteAnnotation,
  onUpdateAnnotationLabel,
  onCheckForOverlaps
}) => {
  const [editingLabelId, setEditingLabelId] = useState(null)
  const [editingLabelValue, setEditingLabelValue] = useState('')

  if (!annotations || annotations.length === 0) {
    return null
  }

  // Check which annotations have overlaps
  const getOverlappingAnnotations = (annotation) => {
    if (onCheckForOverlaps) {
      return onCheckForOverlaps(annotation)
    }
    return []
  }

  const handleLabelClick = (annotation, e) => {
    e.stopPropagation()
    setEditingLabelId(annotation.id)
    setEditingLabelValue(annotation.label || `${annotation.type} ${annotation.id.slice(0, 8)}`)
  }

  const handleLabelSubmit = (annotationId) => {
    if (editingLabelValue.trim()) {
      onUpdateAnnotationLabel(annotationId, editingLabelValue.trim())
    }
    setEditingLabelId(null)
    setEditingLabelValue('')
  }

  const handleLabelKeyDown = (e, annotationId) => {
    // Prevent all keyboard shortcuts from interfering with label editing
    e.stopPropagation()

    if (e.key === 'Enter') {
      e.preventDefault()
      handleLabelSubmit(annotationId)
    } else if (e.key === 'Escape') {
      e.preventDefault()
      setEditingLabelId(null)
      setEditingLabelValue('')
    }
  }

  return (
    <div className="annotations-list">
      <h3>Annotations ({annotations.length})</h3>
      <div className="annotation-items">
        {annotations.map(annotation => {
          const overlappingAnnotations = getOverlappingAnnotations(annotation)
          const hasOverlaps = overlappingAnnotations.length > 0

          return (
            <div
              key={annotation.id}
              className={`annotation-item ${selectedAnnotation && selectedAnnotation.id === annotation.id ? 'selected' : ''} ${hasOverlaps ? 'overlap-warning' : ''}`}
              onClick={() => onSelectAnnotation(annotation)}
              title={hasOverlaps ? `Warning: Overlaps with ${overlappingAnnotations.map(ann => ann.label || ann.type + ' ' + ann.id.slice(0, 8)).join(', ')}` : ''}
            >
              {editingLabelId === annotation.id ? (
                <input
                  type="text"
                  className="annotation-label-input"
                  data-editing-label="true"
                  value={editingLabelValue}
                  onChange={(e) => {
                    e.stopPropagation()
                    setEditingLabelValue(e.target.value)
                  }}
                  onBlur={() => handleLabelSubmit(annotation.id)}
                  onKeyDown={(e) => handleLabelKeyDown(e, annotation.id)}
                  onKeyUp={(e) => e.stopPropagation()}
                  onKeyPress={(e) => e.stopPropagation()}
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              ) : (
                <div
                  className="annotation-label"
                  onClick={(e) => handleLabelClick(annotation, e)}
                  title="Click to edit label"
                >
                  {annotation.label || `${annotation.type} ${annotation.id.slice(0, 8)}`}
                </div>
              )}
              <div className="annotation-details">
                {annotation.type === 'rectangle'
                  ? `Rectangle (${Math.round(annotation.x)}, ${Math.round(annotation.y)}, ${Math.round(annotation.width)}×${Math.round(annotation.height)})`
                  : `Polygon (${annotation.points.length} points)`
                }
                {hasOverlaps && (
                  <div style={{ color: '#dc3545', fontSize: '0.8em', marginTop: '2px', fontWeight: 'bold' }}>
                    ⚠️ Overlaps detected
                  </div>
                )}
              </div>
              <div className="annotation-controls">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onCopyAnnotation(annotation)
                  }}
                  style={{ backgroundColor: 'white', color: '#333' }}
                >
                  Copy
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onDeleteAnnotation(annotation.id)
                  }}
                  style={{ backgroundColor: 'white', color: '#333' }}
                >
                  Delete
                </button>
              </div>
            </div>
          )
        })}
      </div>
      <div className="annotation-help">
        <p><strong>Instructions:</strong></p>
        <ul>
          <li>Hand Tool (✋): Drag to pan around the PDF (Press H)</li>
          <li>Rectangle: Click two points (top-left, bottom-right) (Press R)</li>
          <li>Polygon: Click multiple points, then "Finish Polygon" (Press P)</li>
          <li>Select: Click on annotation to select/move</li>
          <li>Zoom: Mouse wheel to zoom in/out</li>
          <li>Copy: Ctrl+C, Paste: Ctrl+V</li>
          <li>Delete: Select annotation and press Delete key</li>
          <li>Export JSON: 💾 button or Ctrl+S</li>
          <li>Export PDF: 📄✏️ button (annotations drawn on PDF)</li>
          <li>Reset: 🎯 button to center PDF, 1:1 for 100% zoom</li>
          <li>Cancel: Press Escape</li>
        </ul>
      </div>
    </div>
  )
}

export default AnnotationsList
