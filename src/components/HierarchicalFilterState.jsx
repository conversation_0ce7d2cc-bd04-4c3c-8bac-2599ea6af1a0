import { useState, useCallback, useEffect } from 'react'

export const useHierarchicalFilterState = (csvStructure, hierarchicalData) => {
  const [selectedPath, setSelectedPath] = useState([])
  const [filteredRooms, setFilteredRooms] = useState([])
  const [isFilterActive, setIsFilterActive] = useState(false)
  const [sessionFilters, setSessionFilters] = useState(null)

  // Reset filters when CSV data changes
  useEffect(() => {
    if (!csvStructure || !hierarchicalData) {
      setSelectedPath([])
      setFilteredRooms([])
      setIsFilterActive(false)
      setSessionFilters(null)
    }
  }, [csvStructure, hierarchicalData])

  // Get available rooms based on current filter selection
  const getFilteredRooms = useCallback((path = selectedPath) => {
    if (!hierarchicalData || path.length === 0) {
      return []
    }

    let currentLevel = hierarchicalData
    
    // Navigate through the hierarchy based on the selected path
    for (const pathItem of path) {
      if (currentLevel[pathItem]) {
        currentLevel = currentLevel[pathItem]
      } else {
        return []
      }
    }

    return currentLevel._rooms || []
  }, [hierarchicalData, selectedPath])

  // Get all rooms under a specific path (including sub-levels)
  const getAllRoomsUnderPath = useCallback((path = selectedPath) => {
    if (!hierarchicalData || path.length === 0) {
      return []
    }

    let currentLevel = hierarchicalData
    
    // Navigate to the selected level
    for (const pathItem of path) {
      if (currentLevel[pathItem]) {
        currentLevel = currentLevel[pathItem]
      } else {
        return []
      }
    }

    // Recursively collect all rooms from this level and sub-levels
    const collectRooms = (level) => {
      let rooms = []
      
      // Add rooms at current level
      if (level._rooms) {
        rooms.push(...level._rooms)
      }
      
      // Add rooms from sub-levels
      Object.keys(level).forEach(key => {
        if (key !== '_rooms' && typeof level[key] === 'object') {
          rooms.push(...collectRooms(level[key]))
        }
      })
      
      return rooms
    }

    return collectRooms(currentLevel)
  }, [hierarchicalData, selectedPath])

  // Update filter selection
  const updateFilterSelection = useCallback((newPath) => {
    setSelectedPath(newPath)
    const rooms = getFilteredRooms(newPath)
    setFilteredRooms(rooms)
    setIsFilterActive(newPath.length > 0)
    
    // Store in session for persistence
    if (newPath.length > 0) {
      setSessionFilters({
        path: newPath,
        timestamp: Date.now()
      })
    } else {
      setSessionFilters(null)
    }
  }, [getFilteredRooms])

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSelectedPath([])
    setFilteredRooms([])
    setIsFilterActive(false)
    setSessionFilters(null)
  }, [])

  // Get available options for a specific level
  const getOptionsForLevel = useCallback((levelIndex) => {
    if (!hierarchicalData || levelIndex < 0) return []

    let currentLevel = hierarchicalData
    
    // Navigate to the parent level
    for (let i = 0; i < levelIndex; i++) {
      if (i < selectedPath.length && currentLevel[selectedPath[i]]) {
        currentLevel = currentLevel[selectedPath[i]]
      } else {
        return []
      }
    }

    return Object.keys(currentLevel).filter(key => key !== '_rooms')
  }, [hierarchicalData, selectedPath])

  // Check if a room matches the current filter
  const roomMatchesFilter = useCallback((roomName) => {
    if (!isFilterActive) return true
    
    return filteredRooms.some(room => room.name === roomName)
  }, [isFilterActive, filteredRooms])

  // Get room names that match the current filter
  const getFilteredRoomNames = useCallback(() => {
    if (!isFilterActive) {
      // Return all room names if no filter is active
      return csvStructure?.allRoomNames || []
    }
    
    return filteredRooms.map(room => room.name)
  }, [isFilterActive, filteredRooms, csvStructure])

  // Get filter summary for display
  const getFilterSummary = useCallback(() => {
    if (!isFilterActive) return null
    
    return {
      path: selectedPath,
      pathString: selectedPath.join(' → '),
      roomCount: filteredRooms.length,
      totalRooms: csvStructure?.totalRooms || 0
    }
  }, [isFilterActive, selectedPath, filteredRooms, csvStructure])

  // Restore filters from session (useful for page refresh)
  const restoreSessionFilters = useCallback(() => {
    if (sessionFilters && sessionFilters.path) {
      updateFilterSelection(sessionFilters.path)
      return true
    }
    return false
  }, [sessionFilters, updateFilterSelection])

  return {
    // State
    selectedPath,
    filteredRooms,
    isFilterActive,
    sessionFilters,
    
    // Actions
    updateFilterSelection,
    clearFilters,
    restoreSessionFilters,
    
    // Getters
    getFilteredRooms,
    getAllRoomsUnderPath,
    getOptionsForLevel,
    roomMatchesFilter,
    getFilteredRoomNames,
    getFilterSummary
  }
}
