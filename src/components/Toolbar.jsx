import React, { useState } from 'react'

const Toolbar = ({
  fileInputRef,
  onFileUpload,
  csvInputRef,
  onCSVUpload,
  csvFileName,
  roomNames,
  allPdfData,
  currentPdfIndex,
  onSwitchToPdf,
  pdfPages,
  currentPageIndex,
  onSetCurrentPageIndex,
  zoom,
  onSetZoom,
  onAutoFitToScreen,
  onResetCanvasPosition,
  drawingMode,
  onSetDrawingMode,
  currentAnnotation,
  onFinishPolygon,
  rectangleStartPoint,
  onSetRectangleStartPoint,
  onExportAnnotations,
  onExportAnnotatedPDF,
  getCurrentAnnotations,
  selectedAnnotation,
  onCopyAnnotation,
  onDeleteAnnotation,
  showRoomDropdownForAnnotation,
  // Hierarchical filter props
  csvStructure,
  hierarchicalFilter,
  onShowFilterModal
}) => {
  const [showPdfDropdown, setShowPdfDropdown] = useState(false)

  return (
    <div className="toolbar">
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        multiple
        onChange={onFileUpload}
        style={{ display: 'none' }}
      />

      <input
        ref={csvInputRef}
        type="file"
        accept=".csv"
        onChange={onCSVUpload}
        style={{ display: 'none' }}
      />

      <div className="toolbar-section">
        <button
          className="icon-button"
          onClick={() => fileInputRef.current?.click()}
          title="Upload PDF"
        >
          📄
        </button>

        <div className="csv-upload-section">
          <button
            className="icon-button"
            onClick={() => csvInputRef.current?.click()}
            title="Upload CSV with room names"
          >
            📊
          </button>
          {csvFileName && (
            <span className="csv-status loaded" title={`${roomNames.length} room names loaded from ${csvFileName}`}>
              {roomNames.length} rooms
            </span>
          )}
        </div>

        {/* Hierarchical Filter Control */}
        {csvStructure && csvStructure.maxDepth > 1 && (
          <div className="filter-control-section">
            <button
              className={`icon-button ${hierarchicalFilter?.isFilterActive ? 'active' : ''}`}
              onClick={onShowFilterModal}
              title={hierarchicalFilter?.isFilterActive ?
                `Filter active: ${hierarchicalFilter.selectedPath.join(' → ')}` :
                'Set room filter'
              }
            >
              🔍
            </button>
            {hierarchicalFilter?.isFilterActive && (
              <div className="filter-status">
                <span className="filter-path" title={hierarchicalFilter.selectedPath.join(' → ')}>
                  {hierarchicalFilter.selectedPath.slice(-2).join(' → ')}
                </span>
                <button
                  className="clear-filter-btn"
                  onClick={() => hierarchicalFilter.clearFilters()}
                  title="Clear filter"
                >
                  ✕
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {allPdfData.current.length > 0 && (
        <>
          {allPdfData.current.length > 1 && (
            <div className="toolbar-section">
              <button
                className="icon-button"
                onClick={() => onSwitchToPdf(Math.max(0, currentPdfIndex - 1))}
                disabled={currentPdfIndex === 0}
                title="Previous PDF"
              >
                ⏮
              </button>
              <div className="pdf-dropdown-container">
                <button
                  className="icon-button"
                  onClick={() => setShowPdfDropdown(!showPdfDropdown)}
                  title="Select PDF"
                >
                  📋
                </button>
                {showPdfDropdown && (
                  <div className="pdf-dropdown">
                    {allPdfData.current.map((pdfData, index) => (
                      <div
                        key={index}
                        className={`pdf-dropdown-item ${index === currentPdfIndex ? 'active' : ''}`}
                        onClick={() => onSwitchToPdf(index)}
                      >
                        {pdfData.name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <span>{currentPdfIndex + 1}/{allPdfData.current.length}</span>
              <button
                className="icon-button"
                onClick={() => onSwitchToPdf(Math.min(allPdfData.current.length - 1, currentPdfIndex + 1))}
                disabled={currentPdfIndex === allPdfData.current.length - 1}
                title="Next PDF"
              >
                ⏭
              </button>
            </div>
          )}

          {pdfPages.length > 1 && (
            <div className="toolbar-section">
              <button
                className="icon-button"
                onClick={() => onSetCurrentPageIndex(Math.max(0, currentPageIndex - 1))}
                disabled={currentPageIndex === 0}
                title="Previous Page"
              >
                ◀
              </button>
              <span>Page {currentPageIndex + 1}/{pdfPages.length}</span>
              <button
                className="icon-button"
                onClick={() => onSetCurrentPageIndex(Math.min(pdfPages.length - 1, currentPageIndex + 1))}
                disabled={currentPageIndex === pdfPages.length - 1}
                title="Next Page"
              >
                ▶
              </button>
            </div>
          )}

          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={() => onSetZoom(Math.max(0.1, zoom - 0.1))}
              disabled={zoom <= 0.1}
              title="Zoom Out (Mouse wheel)"
            >
              🔍-
            </button>
            <button
              className="icon-button"
              onClick={onAutoFitToScreen}
              title="Fit to Screen"
            >
              📐
            </button>
            <button
              className="icon-button"
              onClick={() => onSetZoom(1)}
              title="Reset to 100%"
            >
              1:1
            </button>
            <button
              className="icon-button"
              onClick={onResetCanvasPosition}
              title="Reset Position"
            >
              🎯
            </button>
            <span>{Math.round(zoom * 100)}%</span>
            <button
              className="icon-button"
              onClick={() => onSetZoom(Math.min(5, zoom + 0.1))}
              disabled={zoom >= 5}
              title="Zoom In (Mouse wheel)"
            >
              🔍+
            </button>
          </div>

          <div className="toolbar-section">
            <button
              className={`icon-button ${drawingMode === 'hand' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('hand')}
              title="Hand Tool (drag to pan)"
            >
              ✋
            </button>
            <button
              className={`icon-button ${drawingMode === 'select' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('select')}
              title="Select Tool (click and drag annotations)"
            >
              🔲
            </button>
            <button
              className={`icon-button ${drawingMode === 'rectangle' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('rectangle')}
              title="Rectangle Tool (2 clicks)"
            >
              ⬜
            </button>
            <button
              className={`icon-button ${drawingMode === 'polygon' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('polygon')}
              title="Polygon Tool (multi-click)"
            >
              🔺
            </button>
            {currentAnnotation && drawingMode === 'polygon' && (
              <button
                className="icon-button"
                onClick={(event) => {
                  if (roomNames.length > 0 && showRoomDropdownForAnnotation) {
                    // Use room dropdown for polygon
                    onFinishPolygon((annotation) => {
                      showRoomDropdownForAnnotation(annotation, event)
                    })
                  } else {
                    // No CSV loaded, finish normally
                    onFinishPolygon()
                  }
                }}
                title="Finish Polygon (Enter)"
              >
                ✓
              </button>
            )}
            {rectangleStartPoint && drawingMode === 'rectangle' && (
              <button
                className="icon-button"
                onClick={() => onSetRectangleStartPoint(null)}
                title="Cancel Rectangle (Esc)"
              >
                ✕
              </button>
            )}
          </div>

          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={onExportAnnotations}
              disabled={Object.keys(getCurrentAnnotations()).length === 0}
              title="Export JSON (Ctrl+S)"
            >
              💾
            </button>
            <button
              className="icon-button"
              onClick={onExportAnnotatedPDF}
              disabled={getCurrentAnnotations().length === 0 || allPdfData.current.length === 0}
              title="Export Annotated PDF"
            >
              📄✏️
            </button>
            {selectedAnnotation && (
              <div className="selected-annotation-info">
                <span>{selectedAnnotation.type}</span>
                <button
                  className="icon-button"
                  onClick={() => onCopyAnnotation(selectedAnnotation)}
                  title="Copy (Ctrl+C)"
                >
                  📋
                </button>
                <button
                  className="icon-button"
                  onClick={() => onDeleteAnnotation(selectedAnnotation.id)}
                  title="Delete (Del)"
                >
                  🗑️
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default Toolbar
