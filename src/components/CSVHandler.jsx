import { useState, useCallback } from 'react'

export const useCSVHandler = () => {
  const [roomNames, setRoomNames] = useState([])
  const [csvFileName, setCsvFileName] = useState('')
  const [hierarchicalData, setHierarchicalData] = useState(null)
  const [csvStructure, setCsvStructure] = useState(null)

  // Analyze CSV structure and build hierarchical data
  const analyzeCSVStructure = useCallback((csvContent) => {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim() !== '')
      if (lines.length === 0) return null

      // Parse all rows and determine the maximum number of columns
      const allRows = lines.map(line => {
        return line.split(',').map(col => col.trim().replace(/^"|"$/g, ''))
      }).filter(row => row.length > 0)

      if (allRows.length === 0) return null

      // Find the maximum number of columns to determine hierarchy depth
      const maxColumns = Math.max(...allRows.map(row => row.length))

      // Create column headers (Level 1, Level 2, etc. or use actual headers if present)
      const columnHeaders = []
      for (let i = 0; i < maxColumns; i++) {
        columnHeaders.push(`Level ${i + 1}`)
      }

      // Build hierarchical structure
      const hierarchyTree = {}
      const allRoomNames = new Set()

      allRows.forEach((row, rowIndex) => {
        // Skip rows that are too short or contain only empty values
        const cleanRow = row.filter(cell =>
          cell &&
          cell.trim() !== '' &&
          cell.trim().toLowerCase() !== 'null' &&
          cell.trim().toLowerCase() !== 'nan' &&
          cell.trim().toLowerCase() !== 'undefined'
        )
        if (cleanRow.length === 0) return

        // Build the hierarchy path
        let currentLevel = hierarchyTree
        let path = []

        for (let i = 0; i < cleanRow.length; i++) {
          const value = cleanRow[i].trim()
          if (!value) continue

          path.push(value)

          // If this is the last column in a complete row, treat it as a room name
          if (i === cleanRow.length - 1 && cleanRow.length === maxColumns) {
            // This is a room name - additional validation
            if (value &&
                value.length > 0 &&
                value.toLowerCase() !== 'nan' &&
                value.toLowerCase() !== 'null' &&
                value.toLowerCase() !== 'undefined' &&
                !value.match(/^\s*$/)) {
              allRoomNames.add(value)

              // Store the full path for this room
              if (!currentLevel._rooms) currentLevel._rooms = []
              currentLevel._rooms.push({
                name: value,
                path: [...path],
                sourceRow: rowIndex + 1 // For debugging
              })
            }
          } else {
            // This is a hierarchy level
            if (!currentLevel[value]) {
              currentLevel[value] = {}
            }
            currentLevel = currentLevel[value]
          }
        }
      })

      // Validate the structure and provide statistics
      const validateStructure = (tree, level = 0) => {
        let stats = { levels: 0, branches: 0, rooms: 0 }

        Object.keys(tree).forEach(key => {
          if (key === '_rooms') {
            stats.rooms += tree[key].length
          } else {
            stats.branches++
            const subStats = validateStructure(tree[key], level + 1)
            stats.levels = Math.max(stats.levels, subStats.levels + 1)
            stats.branches += subStats.branches
            stats.rooms += subStats.rooms
          }
        })

        return stats
      }

      const structureStats = validateStructure(hierarchyTree)

      console.log('CSV Analysis Debug:', {
        maxColumns,
        totalRooms: allRoomNames.size,
        allRoomNames: Array.from(allRoomNames).sort(),
        structureStats,
        sampleHierarchy: Object.keys(hierarchyTree).slice(0, 2)
      })

      return {
        columnHeaders,
        hierarchyTree,
        maxDepth: maxColumns,
        totalRooms: allRoomNames.size,
        allRoomNames: Array.from(allRoomNames).sort(),
        stats: {
          ...structureStats,
          consistency: structureStats.rooms === allRoomNames.size ? 'good' : 'warning'
        }
      }
    } catch (error) {
      console.error('Error analyzing CSV structure:', error)
      return null
    }
  }, [])

  // Legacy function for backward compatibility - extract room names from the last column
  const parseCSV = useCallback((csvContent) => {
    try {
      const lines = csvContent.split('\n').filter(line => line.trim() !== '')
      const roomNamesSet = new Set()

      lines.forEach(line => {
        // Split by comma and handle quoted values
        const columns = line.split(',').map(col => col.trim().replace(/^"|"$/g, ''))

        // Skip rows with less than 2 columns
        if (columns.length < 2) return

        // Get the last column (room name)
        const roomName = columns[columns.length - 1].trim()

        // Skip empty room names or rows with NaN/null values
        if (roomName &&
            roomName !== '' &&
            roomName.toLowerCase() !== 'nan' &&
            roomName.toLowerCase() !== 'null' &&
            roomName.toLowerCase() !== 'undefined') {
          roomNamesSet.add(roomName)
        }
      })

      return Array.from(roomNamesSet).sort()
    } catch (error) {
      console.error('Error parsing CSV:', error)
      return []
    }
  }, [])

  // Handle CSV file upload
  const handleCSVUpload = useCallback(async (event) => {
    const file = event.target.files[0]
    if (!file) return

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      alert('Please select a CSV file')
      return
    }

    try {
      const text = await file.text()

      // Analyze the hierarchical structure
      const structure = analyzeCSVStructure(text)

      if (!structure || structure.totalRooms === 0) {
        alert('No valid room names found in the CSV file. Please check that the file contains proper hierarchical data.')
        return
      }

      // Set both hierarchical and legacy data
      setCsvStructure(structure)
      setHierarchicalData(structure.hierarchyTree)
      setRoomNames(structure.allRoomNames) // For backward compatibility
      setCsvFileName(file.name)

      console.log(`Loaded hierarchical CSV with ${structure.totalRooms} room names from ${file.name}:`)
      console.log('Structure:', structure)
      console.log(`Hierarchy depth: ${structure.maxDepth} levels`)
      console.log(`Structure stats:`, structure.stats)

      // Provide user feedback about the structure
      if (structure.maxDepth === 1) {
        console.log('Note: CSV contains only room names (no hierarchy). Using simple dropdown mode.')
      } else {
        console.log(`Hierarchical structure detected with ${structure.maxDepth} levels. Using hierarchical filter mode.`)
      }

    } catch (error) {
      console.error('Error reading CSV file:', error)
      alert('Error reading CSV file. Please make sure it\'s a valid CSV file.')
    }
  }, [analyzeCSVStructure])

  // Clear CSV data
  const clearCSVData = useCallback(() => {
    setRoomNames([])
    setCsvFileName('')
    setHierarchicalData(null)
    setCsvStructure(null)
  }, [])

  return {
    roomNames,
    csvFileName,
    hierarchicalData,
    csvStructure,
    handleCSVUpload,
    clearCSVData,
    parseCSV // Keep for backward compatibility
  }
}
