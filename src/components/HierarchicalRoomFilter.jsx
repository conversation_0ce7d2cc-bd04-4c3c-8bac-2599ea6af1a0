import React, { useState, useEffect, useCallback } from 'react'

const HierarchicalRoomFilter = ({
  csvStructure,
  hierarchicalData,
  onRoomSelection,
  onFilterChange,
  className = ''
}) => {
  const [selectedPath, setSelectedPath] = useState([])
  const [availableOptions, setAvailableOptions] = useState([])
  const [filteredRooms, setFilteredRooms] = useState([])

  // Initialize available options for the first level
  useEffect(() => {
    if (!hierarchicalData || !csvStructure) {
      setAvailableOptions([])
      setFilteredRooms([])
      return
    }

    // Get first level options
    const firstLevelOptions = Object.keys(hierarchicalData).filter(key => key !== '_rooms')
    setAvailableOptions([firstLevelOptions])
    setSelectedPath([])
    setFilteredRooms([])
  }, [hierarchicalData, csvStructure])

  // Update available options when selection changes
  const updateAvailableOptions = useCallback((newPath) => {
    if (!hierarchicalData || !csvStructure) return

    const newOptions = []
    let currentLevel = hierarchicalData

    // Build options for each level based on the selected path
    for (let i = 0; i <= newPath.length; i++) {
      if (i < newPath.length) {
        // Navigate to the selected level
        if (currentLevel[newPath[i]]) {
          currentLevel = currentLevel[newPath[i]]
        } else {
          break
        }
      }

      if (i === newPath.length) {
        // Get options for the next level
        const options = Object.keys(currentLevel).filter(key => key !== '_rooms')
        if (options.length > 0) {
          newOptions.push(options)
        }
      }
    }

    setAvailableOptions(newOptions)

    // Update filtered rooms based on current selection
    const rooms = currentLevel._rooms || []
    setFilteredRooms(rooms)

    // Notify parent component of filter change
    if (onFilterChange) {
      onFilterChange({
        selectedPath: newPath,
        filteredRooms: rooms,
        availableRooms: rooms.map(room => room.name)
      })
    }
  }, [hierarchicalData, csvStructure, onFilterChange])

  // Handle selection at a specific level
  const handleLevelSelection = useCallback((levelIndex, value) => {
    const newPath = [...selectedPath.slice(0, levelIndex), value]
    setSelectedPath(newPath)
    updateAvailableOptions(newPath)
  }, [selectedPath, updateAvailableOptions])

  // Handle room selection
  const handleRoomSelection = useCallback((roomName) => {
    if (onRoomSelection) {
      const selectedRoom = filteredRooms.find(room => room.name === roomName)
      onRoomSelection(roomName, selectedRoom?.path || [...selectedPath, roomName])
    }
  }, [filteredRooms, selectedPath, onRoomSelection])

  // Clear all selections
  const clearSelections = useCallback(() => {
    setSelectedPath([])
    updateAvailableOptions([])
  }, [updateAvailableOptions])

  if (!csvStructure || !hierarchicalData) {
    return (
      <div className={`hierarchical-room-filter ${className}`}>
        <div className="filter-message">
          No hierarchical data available. Please upload a CSV file with hierarchical structure.
        </div>
      </div>
    )
  }

  return (
    <div className={`hierarchical-room-filter ${className}`}>
      <div className="filter-header">
        <h4>Filter Rooms by Hierarchy</h4>
        {selectedPath.length > 0 && (
          <button 
            className="clear-filters-btn"
            onClick={clearSelections}
            title="Clear all filters"
          >
            Clear Filters
          </button>
        )}
      </div>

      <div className="filter-levels">
        {availableOptions.map((options, levelIndex) => (
          <div key={levelIndex} className="filter-level">
            <label className="filter-level-label">
              {csvStructure.columnHeaders[levelIndex] || `Level ${levelIndex + 1}`}:
            </label>
            <select
              className="filter-level-select"
              value={selectedPath[levelIndex] || ''}
              onChange={(e) => handleLevelSelection(levelIndex, e.target.value)}
            >
              <option value="">-- Select {csvStructure.columnHeaders[levelIndex] || `Level ${levelIndex + 1}`} --</option>
              {options.map(option => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>

      {selectedPath.length > 0 && (
        <div className="selected-path">
          <strong>Selected Path:</strong> {selectedPath.join(' → ')}
        </div>
      )}

      {filteredRooms.length > 0 && (
        <div className="filtered-rooms">
          <h5>Available Rooms ({filteredRooms.length}):</h5>
          <div className="rooms-list">
            {filteredRooms.map((room, index) => (
              <button
                key={index}
                className="room-item"
                onClick={() => handleRoomSelection(room.name)}
                title={`Full path: ${room.path.join(' → ')}`}
              >
                {room.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {selectedPath.length > 0 && filteredRooms.length === 0 && (
        <div className="no-rooms-message">
          No rooms found for the selected path. Try selecting different options.
        </div>
      )}
    </div>
  )
}

export default HierarchicalRoomFilter
