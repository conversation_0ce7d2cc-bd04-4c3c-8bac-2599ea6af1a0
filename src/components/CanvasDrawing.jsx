import { useCallback } from 'react'

export const useCanvasDrawing = (canvasRef, baseImages, currentPageIndex, pdfPages) => {
  
  // Draw only annotations without reloading the base image
  const drawAnnotationsOnly = useCallback((annotations, selectedAnnotation, currentAnnotation, polygonPoints, rectangleStartPoint, drawingMode) => {
    const canvas = canvasRef.current
    if (!canvas || pdfPages.length === 0) return

    const ctx = canvas.getContext('2d')
    const currentPage = pdfPages[currentPageIndex]
    const baseImg = baseImages.current[currentPageIndex]

    // Set canvas size if needed
    if (canvas.width !== currentPage.width || canvas.height !== currentPage.height) {
      canvas.width = currentPage.width
      canvas.height = currentPage.height
    }

    // Clear canvas first
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw base image if available, otherwise draw from page data
    if (baseImg) {
      ctx.drawImage(baseImg, 0, 0)
    } else {
      // Fallback: create image from page data
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 0, 0)
        baseImages.current[currentPageIndex] = img
        // Redraw annotations after base image is loaded
        drawAnnotationsOnly(annotations, selectedAnnotation, currentAnnotation, polygonPoints, rectangleStartPoint, drawingMode)
      }
      img.src = currentPage.imageData
      return // Exit early, will be called again after image loads
    }

    // Draw existing annotations
    annotations.forEach(annotation => {
      ctx.save() // Save current context state

      const isSelected = selectedAnnotation && selectedAnnotation.id === annotation.id
      ctx.strokeStyle = isSelected ? '#00ff00' : '#ff0000'  // Bright red for visibility
      ctx.lineWidth = isSelected ? 5 : 4  // Even thicker lines for better visibility
      ctx.setLineDash(isSelected ? [10, 5] : [])
      ctx.globalAlpha = 1.0  // Ensure full opacity
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      if (annotation.type === 'rectangle') {
        // Draw rectangle with thick, visible stroke
        ctx.strokeRect(annotation.x, annotation.y, annotation.width, annotation.height)

        // Draw selection handles for selected rectangle
        if (isSelected) {
          ctx.fillStyle = '#00ff00'
          const handleSize = 10  // Even larger handles for better visibility
          ctx.fillRect(annotation.x - handleSize/2, annotation.y - handleSize/2, handleSize, handleSize)
          ctx.fillRect(annotation.x + annotation.width - handleSize/2, annotation.y - handleSize/2, handleSize, handleSize)
          ctx.fillRect(annotation.x - handleSize/2, annotation.y + annotation.height - handleSize/2, handleSize, handleSize)
          ctx.fillRect(annotation.x + annotation.width - handleSize/2, annotation.y + annotation.height - handleSize/2, handleSize, handleSize)
        }
      } else if (annotation.type === 'polygon' && annotation.points.length > 1) {
        // Draw polygon with thick, visible stroke
        ctx.beginPath()
        ctx.moveTo(annotation.points[0].x, annotation.points[0].y)
        for (let i = 1; i < annotation.points.length; i++) {
          ctx.lineTo(annotation.points[i].x, annotation.points[i].y)
        }
        ctx.closePath()
        ctx.stroke()

        // Draw selection handles for selected polygon
        if (isSelected) {
          ctx.fillStyle = '#00ff00'
          const handleSize = 10  // Even larger handles for better visibility
          annotation.points.forEach(point => {
            ctx.fillRect(point.x - handleSize/2, point.y - handleSize/2, handleSize, handleSize)
          })
        }
      }

      ctx.restore() // Restore context state
    })

    // Draw current annotation being created
    if (currentAnnotation) {
      ctx.save()
      ctx.strokeStyle = '#0000ff'
      ctx.lineWidth = 4  // Even thicker line for better visibility
      ctx.setLineDash([10, 5])  // More visible dash pattern
      ctx.globalAlpha = 1.0
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      if (currentAnnotation.type === 'rectangle') {
        ctx.strokeRect(currentAnnotation.x, currentAnnotation.y, currentAnnotation.width, currentAnnotation.height)
      } else if (currentAnnotation.type === 'polygon' && polygonPoints.length > 0) {
        ctx.beginPath()
        ctx.moveTo(polygonPoints[0].x, polygonPoints[0].y)
        for (let i = 1; i < polygonPoints.length; i++) {
          ctx.lineTo(polygonPoints[i].x, polygonPoints[i].y)
        }
        ctx.stroke()

        // Draw points for current polygon
        ctx.fillStyle = '#0000ff'
        const pointSize = 8  // Larger points for better visibility
        polygonPoints.forEach(point => {
          ctx.fillRect(point.x - pointSize/2, point.y - pointSize/2, pointSize, pointSize)
        })
      }
      ctx.restore()
    }

    // Draw rectangle start point if in rectangle mode
    if (rectangleStartPoint && drawingMode === 'rectangle') {
      ctx.save()
      ctx.fillStyle = '#0000ff'
      ctx.globalAlpha = 1.0
      const pointSize = 10  // Even larger point for better visibility
      ctx.fillRect(rectangleStartPoint.x - pointSize/2, rectangleStartPoint.y - pointSize/2, pointSize, pointSize)

      // Draw crosshair to show exact coordinate
      ctx.strokeStyle = '#0000ff'
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(rectangleStartPoint.x - 20, rectangleStartPoint.y)
      ctx.lineTo(rectangleStartPoint.x + 20, rectangleStartPoint.y)
      ctx.moveTo(rectangleStartPoint.x, rectangleStartPoint.y - 20)
      ctx.lineTo(rectangleStartPoint.x, rectangleStartPoint.y + 20)
      ctx.stroke()

      ctx.restore()
    }
  }, [canvasRef, baseImages, currentPageIndex, pdfPages])

  return {
    drawAnnotationsOnly
  }
}
