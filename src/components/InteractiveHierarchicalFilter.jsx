import React, { useState, useCallback, useEffect } from 'react'

const InteractiveHierarchicalFilter = ({
  csvStructure,
  hierarchicalData,
  onRoomSelection,
  onFilterChange,
  onClose,
  className = ''
}) => {
  const [selectedPath, setSelectedPath] = useState([])
  const [expandedNodes, setExpandedNodes] = useState(new Set())
  const [filteredRooms, setFilteredRooms] = useState([])

  // Get all rooms under a path (including sub-levels)
  const getAllRoomsUnderPath = useCallback((path) => {
    if (!hierarchicalData || path.length === 0) return []

    let currentLevel = hierarchicalData
    
    // Navigate to the selected level
    for (const pathItem of path) {
      if (currentLevel[pathItem]) {
        currentLevel = currentLevel[pathItem]
      } else {
        return []
      }
    }

    // Recursively collect all rooms from this level and sub-levels
    const collectRooms = (level) => {
      let rooms = []
      
      // Add rooms at current level
      if (level._rooms) {
        rooms.push(...level._rooms)
      }
      
      // Add rooms from sub-levels
      Object.keys(level).forEach(key => {
        if (key !== '_rooms' && typeof level[key] === 'object') {
          rooms.push(...collectRooms(level[key]))
        }
      })
      
      return rooms
    }

    return collectRooms(currentLevel)
  }, [hierarchicalData])

  // Toggle node expansion
  const toggleNode = useCallback((path) => {
    const pathKey = path.join('→')
    const newExpanded = new Set(expandedNodes)
    
    if (newExpanded.has(pathKey)) {
      newExpanded.delete(pathKey)
    } else {
      newExpanded.add(pathKey)
    }
    
    setExpandedNodes(newExpanded)
  }, [expandedNodes])

  // Handle path selection
  const handlePathSelection = useCallback((path) => {
    setSelectedPath(path)
    const rooms = getAllRoomsUnderPath(path)
    setFilteredRooms(rooms)

    console.log('Interactive Filter Selection:', {
      path,
      roomsFound: rooms.length,
      roomNames: rooms.map(r => r.name)
    })

    // Notify parent component
    if (onFilterChange) {
      onFilterChange({
        selectedPath: path,
        filteredRooms: rooms,
        availableRooms: rooms.map(room => room.name)
      })
    }
  }, [getAllRoomsUnderPath, onFilterChange])

  // Apply filter at current selection
  const applyFilter = useCallback(() => {
    if (onFilterChange) {
      onFilterChange({
        selectedPath,
        filteredRooms,
        availableRooms: filteredRooms.map(room => room.name),
        applied: true
      })
    }
  }, [selectedPath, filteredRooms, onFilterChange])

  // Render tree node recursively
  const renderTreeNode = useCallback((data, currentPath = [], level = 0) => {
    if (!data || typeof data !== 'object') return null

    const entries = Object.entries(data).filter(([key]) => key !== '_rooms')
    const rooms = data._rooms || []
    const pathKey = currentPath.join('→')
    const isExpanded = expandedNodes.has(pathKey)
    const isSelected = JSON.stringify(currentPath) === JSON.stringify(selectedPath)
    const hasChildren = entries.length > 0
    const hasRooms = rooms.length > 0

    return (
      <div key={pathKey} className="tree-node">
        {currentPath.length > 0 && (
          <div 
            className={`tree-item ${isSelected ? 'selected' : ''}`}
            style={{ paddingLeft: `${level * 20}px` }}
          >
            <div className="tree-item-content">
              {hasChildren && (
                <button
                  className={`expand-button ${isExpanded ? 'expanded' : ''}`}
                  onClick={() => toggleNode(currentPath)}
                >
                  {isExpanded ? '▼' : '▶'}
                </button>
              )}
              
              <button
                className="folder-button"
                onClick={() => handlePathSelection(currentPath)}
                title={`${currentPath.join(' → ')} (${getAllRoomsUnderPath(currentPath).length} rooms)`}
              >
                <span className="folder-icon">
                  {hasChildren ? (isExpanded ? '📂' : '📁') : '📄'}
                </span>
                <span className="folder-name">
                  {currentPath[currentPath.length - 1]}
                </span>
                <span className="room-count">
                  ({getAllRoomsUnderPath(currentPath).length})
                </span>
              </button>
            </div>
          </div>
        )}

        {(isExpanded || level === 0) && hasChildren && (
          <div className="tree-children">
            {entries.map(([key, value]) => 
              renderTreeNode(value, [...currentPath, key], level + 1)
            )}
          </div>
        )}
      </div>
    )
  }, [expandedNodes, selectedPath, toggleNode, handlePathSelection, getAllRoomsUnderPath])

  // Auto-expand root level
  useEffect(() => {
    if (hierarchicalData && expandedNodes.size === 0) {
      const rootKeys = Object.keys(hierarchicalData).filter(key => key !== '_rooms')
      if (rootKeys.length > 0) {
        setExpandedNodes(new Set(['']))
      }
    }
  }, [hierarchicalData, expandedNodes.size])

  if (!csvStructure || !hierarchicalData) {
    return (
      <div className={`interactive-hierarchical-filter ${className}`}>
        <div className="filter-header">
          <h4>Room Filter</h4>
          <button className="close-button" onClick={onClose}>✕</button>
        </div>
        <div className="no-data-message">
          No hierarchical data available. Please upload a CSV file first.
        </div>
      </div>
    )
  }

  return (
    <div className={`interactive-hierarchical-filter ${className}`}>
      <div className="filter-header">
        <h4>📁 Interactive Room Filter</h4>
        <button className="close-button" onClick={onClose}>✕</button>
      </div>

      <div className="filter-instructions">
        Click folders to navigate • Select any level to filter rooms
      </div>

      {selectedPath.length > 0 && (
        <div className="current-selection">
          <div className="selected-path">
            <strong>Selected:</strong> {selectedPath.join(' → ')}
          </div>
          <div className="selection-stats">
            <span className="rooms-count">{filteredRooms.length} rooms available</span>
          </div>
          <button
            className="apply-filter-btn"
            onClick={applyFilter}
            title={`Apply filter with ${filteredRooms.length} rooms`}
          >
            Apply Filter ({filteredRooms.length} rooms)
          </button>
        </div>
      )}

      <div className="tree-container">
        {renderTreeNode(hierarchicalData)}
      </div>

      {onRoomSelection && filteredRooms.length > 0 && selectedPath.length > 0 && (
        <div className="room-selection-section">
          <h5>Or Select Specific Room:</h5>
          <div className="rooms-grid">
            {filteredRooms.slice(0, 12).map((room, index) => (
              <button
                key={index}
                className="room-button"
                onClick={() => onRoomSelection(room.name)}
                title={`Full path: ${room.path.join(' → ')}`}
              >
                {room.name}
              </button>
            ))}
            {filteredRooms.length > 12 && (
              <div className="more-rooms-indicator">
                +{filteredRooms.length - 12} more
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default InteractiveHierarchicalFilter
