import React, { useState } from 'react'
import HierarchicalRoomFilter from './HierarchicalRoomFilter'
import { useCSVHandler } from './CSVHandler'

const HierarchicalFilterDemo = () => {
  const [selectedRoom, setSelectedRoom] = useState(null)
  const [filterSummary, setFilterSummary] = useState(null)
  
  const {
    roomNames,
    csvFileName,
    hierarchicalData,
    csvStructure,
    handleCSVUpload,
    clearCSVData
  } = useCSVHandler()

  const handleRoomSelection = (roomName, roomPath) => {
    setSelectedRoom({ name: roomName, path: roomPath })
  }

  const handleFilterChange = (filterData) => {
    setFilterSummary(filterData)
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Hierarchical Room Filter Demo</h1>
      
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ddd', borderRadius: '5px' }}>
        <h2>Upload CSV File</h2>
        <input
          type="file"
          accept=".csv"
          onChange={handleCSVUpload}
          style={{ marginRight: '10px' }}
        />
        {csvFileName && (
          <button onClick={clearCSVData} style={{ marginLeft: '10px' }}>
            Clear Data
          </button>
        )}
        
        {csvStructure && (
          <div style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
            <strong>Loaded:</strong> {csvFileName} ({csvStructure.totalRooms} rooms, {csvStructure.maxDepth} levels)
          </div>
        )}
      </div>

      {csvStructure && hierarchicalData && (
        <div style={{ marginBottom: '20px' }}>
          <HierarchicalRoomFilter
            csvStructure={csvStructure}
            hierarchicalData={hierarchicalData}
            onRoomSelection={handleRoomSelection}
            onFilterChange={handleFilterChange}
          />
        </div>
      )}

      {filterSummary && (
        <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
          <h3>Filter Summary</h3>
          <p><strong>Selected Path:</strong> {filterSummary.selectedPath.join(' → ')}</p>
          <p><strong>Available Rooms:</strong> {filterSummary.filteredRooms.length}</p>
          <p><strong>Room Names:</strong> {filterSummary.availableRooms.join(', ')}</p>
        </div>
      )}

      {selectedRoom && (
        <div style={{ padding: '15px', backgroundColor: '#d4edda', borderRadius: '5px', border: '1px solid #c3e6cb' }}>
          <h3>Selected Room</h3>
          <p><strong>Room Name:</strong> {selectedRoom.name}</p>
          <p><strong>Full Path:</strong> {selectedRoom.path?.join(' → ') || 'N/A'}</p>
        </div>
      )}

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
        <h3>Instructions</h3>
        <ol>
          <li>Upload a CSV file with hierarchical structure (e.g., Level,Zone,Room)</li>
          <li>Use the dropdown filters to navigate through the hierarchy</li>
          <li>Select a room from the filtered list</li>
          <li>The selected room and its full path will be displayed below</li>
        </ol>
        
        <h4>Sample CSV Format:</h4>
        <pre style={{ backgroundColor: '#fff', padding: '10px', border: '1px solid #ddd', fontSize: '12px' }}>
{`Level 1,Zone A,Room 101
Level 1,Zone A,Room 102
Level 1,Zone B,Room 103
Level 2,Zone A,Room 201
Level 2,Zone B,Room 202`}
        </pre>
      </div>
    </div>
  )
}

export default HierarchicalFilterDemo
