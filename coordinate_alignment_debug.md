# Coordinate System Alignment Debug Guide

## Problem Description
The coordinate system is misaligned - when clicking on the canvas, the annotation appears in a different location than where the user clicked. This indicates the `screenToCanvasCoordinates` function is not properly accounting for the CSS transforms applied to the canvas.

## Current Canvas Transforms
```css
transform: translate(canvasOffset.x, canvasOffset.y) scale(zoom)
transform-origin: center center
```

## Debug Information Added

### 1. Console Logging
Added detailed logging in `screenToCanvasCoordinates`:
- Screen click coordinates
- Canvas bounding rectangle
- Canvas dimensions
- Calculated canvas coordinates
- Current zoom and offset values

### 2. Visual Crosshair
Added blue crosshair at calculated coordinates to visually verify alignment:
- Shows where the system thinks you clicked
- Helps identify coordinate transformation errors

## Testing Steps

### Step 1: Basic Coordinate Test
1. Upload a PDF
2. Select Rectangle tool
3. Click anywhere on the PDF
4. Check browser console for coordinate logs
5. Look for blue crosshair at click location

**Expected**: Crosshair appears exactly where you clicked
**If Not**: Coordinate transformation is incorrect

### Step 2: Zoom Test
1. Zoom to 200%
2. Click on PDF
3. Check if crosshair aligns with click
4. Check console logs for zoom factor

**Expected**: <PERSON>hair still aligns at all zoom levels
**If Not**: Zoom scaling is incorrect

### Step 3: Pan Test
1. Use hand tool to pan canvas
2. Switch to rectangle tool
3. Click on PDF
4. Check crosshair alignment

**Expected**: Crosshair aligns regardless of pan position
**If Not**: Pan offset calculation is wrong

## Common Coordinate Issues

### Issue 1: Transform Origin Problems
**Symptom**: Coordinates off by canvas center amount
**Cause**: Not accounting for `transform-origin: center center`
**Fix**: Adjust calculation to use center as reference point

### Issue 2: Scale Order Problems
**Symptom**: Coordinates more wrong at higher zoom
**Cause**: Scale and translate applied in wrong order
**Fix**: Reverse transforms in correct order

### Issue 3: getBoundingClientRect Issues
**Symptom**: Coordinates wrong by fixed offset
**Cause**: Canvas position not calculated correctly
**Fix**: Use proper canvas positioning

## Current Transform Analysis

The canvas has:
```css
transform: translate(offsetX, offsetY) scale(zoom)
transform-origin: center center
```

This means:
1. Canvas is first translated by offset
2. Then scaled by zoom factor
3. Both operations use center as origin

To reverse this:
1. Get click position relative to canvas center
2. Divide by zoom to undo scale
3. Account for translate offset
4. Convert back to canvas coordinates

## Debugging Console Output

Look for patterns in console logs:

### Good Coordinates
```
Screen click: 500, 300
Canvas rect: {left: 200, top: 100, width: 600, height: 400}
Canvas size: 800, 600
Calculated canvas coords: 400, 300
```

### Bad Coordinates (Common Issues)
```
// Issue: Negative coordinates
Calculated canvas coords: -100, -50

// Issue: Coordinates outside canvas
Calculated canvas coords: 1200, 800 (canvas is 800x600)

// Issue: Coordinates not scaling with zoom
Zoom: 2, but coordinates don't account for it
```

## Coordinate Transform Formula

Current implementation:
```javascript
const originalCenterX = rect.left + rect.width / 2
const originalCenterY = rect.top + rect.height / 2

const relativeX = x - originalCenterX
const relativeY = y - originalCenterY

const unscaledX = relativeX / zoom
const unscaledY = relativeY / zoom

const canvasX = unscaledX + canvas.width / 2
const canvasY = unscaledY + canvas.height / 2
```

## Alternative Approaches

### Approach 1: Matrix Transform
```javascript
// Use DOMMatrix to handle complex transforms
const matrix = new DOMMatrix()
  .translate(canvasOffset.x, canvasOffset.y)
  .scale(zoom)
const inverse = matrix.inverse()
const point = inverse.transformPoint({x: screenX, y: screenY})
```

### Approach 2: Simplified Transform
```javascript
// Remove complex transforms, use simpler approach
style={{ transform: `scale(${zoom})` }}
// Handle pan differently (e.g., canvas scrolling)
```

### Approach 3: No CSS Transforms
```javascript
// Handle zoom/pan in canvas drawing code instead of CSS
// Draw everything scaled/translated in canvas context
```

## Quick Fixes to Try

### Fix 1: Account for Canvas Offset
```javascript
// Add canvas offset to calculation
const canvasX = (relativeX - canvasOffset.x) / zoom + canvas.width / 2
const canvasY = (relativeY - canvasOffset.y) / zoom + canvas.height / 2
```

### Fix 2: Use Different Transform Origin
```javascript
// Calculate as if transform-origin is top-left
const canvasX = (x - rect.left - canvasOffset.x) / zoom
const canvasY = (y - rect.top - canvasOffset.y) / zoom
```

### Fix 3: Reverse Transform Order
```javascript
// First undo translate, then undo scale
const translatedX = x - rect.left - canvasOffset.x
const translatedY = y - rect.top - canvasOffset.y
const canvasX = translatedX / zoom
const canvasY = translatedY / zoom
```

## Testing Checklist

- [ ] Console shows reasonable coordinate values
- [ ] Crosshair appears at click location
- [ ] Works at 100% zoom, no pan
- [ ] Works at 200% zoom
- [ ] Works at 50% zoom
- [ ] Works with canvas panned left
- [ ] Works with canvas panned right
- [ ] Works with canvas panned up
- [ ] Works with canvas panned down
- [ ] Rectangle appears where crosshair shows
- [ ] Multiple rectangles align correctly

## Success Criteria

The coordinate system is fixed when:
1. **Visual Alignment**: Crosshair appears exactly where you click
2. **Console Accuracy**: Logged coordinates are within canvas bounds
3. **Zoom Consistency**: Alignment works at all zoom levels
4. **Pan Consistency**: Alignment works at all pan positions
5. **Annotation Accuracy**: Rectangles appear where you draw them

## Emergency Fallback

If coordinate system remains broken:
1. **Remove all CSS transforms**
2. **Use canvas-only zoom/pan**
3. **Implement zoom by scaling drawing operations**
4. **Implement pan by translating drawing operations**

This approach eliminates CSS transform complexity entirely.
