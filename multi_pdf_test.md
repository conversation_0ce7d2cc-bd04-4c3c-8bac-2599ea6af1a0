# Multi-PDF Annotation System Test Guide

## Overview
This test guide covers the new multi-PDF functionality that allows users to upload and annotate multiple PDF files (up to 100) with independent annotation storage for each PDF.

## New Features

### 1. **Multi-PDF Upload**
- Upload multiple PDF files at once (up to 100 files)
- Each PDF is processed independently
- File validation ensures only PDF files are accepted

### 2. **PDF Navigation**
- **Previous/Next Buttons**: ⏮⏭ buttons to switch between PDFs
- **Dropdown Selector**: 📋 button opens dropdown with all PDF names
- **PDF Counter**: Shows current PDF position (e.g., "2/5")

### 3. **Independent Annotation Storage**
- Annotations are stored per PDF and per page
- Switching between PDFs preserves all annotations
- Each PDF maintains its own annotation state

## Test Scenarios

### Test 1: Multi-PDF Upload
1. **Setup**: Prepare 3-5 different PDF files
2. **Action**: 
   - Click "Upload PDF" button
   - Select multiple PDF files (hold Ctrl/Cmd)
   - Verify all files are uploaded
3. **Expected Results**:
   - All PDFs appear in dropdown
   - First PDF is automatically selected
   - PDF navigation controls appear

### Test 2: PDF Navigation
1. **Setup**: Upload multiple PDFs
2. **Action**: 
   - Use ⏮⏭ buttons to navigate
   - Use 📋 dropdown to jump to specific PDF
   - Verify PDF counter updates
3. **Expected Results**:
   - Canvas updates to show selected PDF
   - PDF name appears in dropdown
   - Navigation buttons enable/disable correctly

### Test 3: Independent Annotations
1. **Setup**: Upload 2+ PDFs
2. **Action**:
   - Create annotations on PDF 1
   - Switch to PDF 2
   - Create different annotations on PDF 2
   - Switch back to PDF 1
3. **Expected Results**:
   - PDF 1 shows original annotations
   - PDF 2 shows its own annotations
   - No cross-contamination between PDFs

### Test 4: Page Navigation Within PDFs
1. **Setup**: Upload multi-page PDFs
2. **Action**:
   - Create annotations on page 1 of PDF 1
   - Navigate to page 2 of PDF 1
   - Create annotations on page 2
   - Switch to different PDF
   - Switch back and verify page annotations
3. **Expected Results**:
   - Page-specific annotations are preserved
   - Page navigation works independently per PDF

### Test 5: Export Functionality
1. **Setup**: Create annotations on multiple PDFs
2. **Action**:
   - Export JSON from PDF 1
   - Export annotated PDF from PDF 1
   - Switch to PDF 2 and export
3. **Expected Results**:
   - JSON exports contain only current PDF annotations
   - PDF exports show only current PDF annotations
   - Filenames include PDF name

### Test 6: Large File Handling
1. **Setup**: Upload close to 100 PDF files
2. **Action**:
   - Verify upload completes successfully
   - Test navigation performance
   - Create annotations on various PDFs
3. **Expected Results**:
   - All files load without errors
   - Navigation remains responsive
   - Memory usage stays reasonable

## UI Components

### Toolbar Layout
```
[📄] [⏮] [📋] [2/5] [⏭] | [◀] [Page 1/3] [▶] | [🔍-] [100%] [🔍+] | [⬜] [🔺] | [💾] [📄✏️]
```

### PDF Dropdown
- Shows list of all uploaded PDF names
- Highlights currently selected PDF
- Scrollable for many files
- Click to switch to specific PDF

### Navigation Controls
- **⏮**: Previous PDF (disabled on first PDF)
- **⏭**: Next PDF (disabled on last PDF)
- **📋**: PDF dropdown toggle
- **PDF Counter**: Shows "current/total" PDFs

## Data Structure

### Annotation Storage
```javascript
annotations = {
  "0-0": [...], // PDF 0, Page 0 annotations
  "0-1": [...], // PDF 0, Page 1 annotations
  "1-0": [...], // PDF 1, Page 0 annotations
  "1-1": [...], // PDF 1, Page 1 annotations
}
```

### PDF Data Storage
```javascript
allPdfData.current = [
  {
    file: File,
    pages: [...],
    dimensions: {width, height},
    name: "floorplan1.pdf"
  },
  {
    file: File,
    pages: [...],
    dimensions: {width, height},
    name: "floorplan2.pdf"
  }
]
```

## Expected Behaviors

### Upload Process
1. File validation (PDF only, max 100 files)
2. Sequential processing of each PDF
3. Page rendering at 300 DPI
4. Dimension extraction for coordinate mapping
5. Automatic selection of first PDF

### Navigation Process
1. State cleanup when switching PDFs
2. Canvas update with new PDF pages
3. Annotation loading for current PDF/page
4. UI control state updates

### Export Process
1. Filter annotations for current PDF only
2. Include PDF name in export filename
3. Maintain coordinate accuracy per PDF

## Troubleshooting

### Common Issues
1. **Memory Usage**: Large number of PDFs may consume significant memory
2. **Performance**: Navigation may slow with many annotations
3. **File Names**: Very long PDF names may overflow in dropdown

### Performance Optimization
- Base images cached per PDF page
- Annotations grouped by PDF index
- Lazy loading of PDF content
- Efficient state management

## Success Criteria

The multi-PDF system works correctly when:
- ✅ Multiple PDFs can be uploaded simultaneously
- ✅ Navigation between PDFs is smooth and responsive
- ✅ Annotations are completely isolated per PDF
- ✅ Export functions work correctly for current PDF
- ✅ UI controls update appropriately
- ✅ Memory usage remains reasonable
- ✅ All existing single-PDF functionality still works

## Workflow Example

1. **Upload**: Select 5 floorplan PDFs
2. **Annotate PDF 1**: Add room labels and measurements
3. **Switch to PDF 2**: Navigate using dropdown
4. **Annotate PDF 2**: Add different annotations
5. **Export PDF 1**: Switch back and export annotated version
6. **Continue**: Repeat for remaining PDFs

This workflow demonstrates the practical use case of annotating multiple floorplans or documents in a single session while maintaining complete separation of annotations.
