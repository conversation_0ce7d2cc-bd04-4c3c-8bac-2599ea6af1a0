# Pre-Filter Workflow Test

## New Workflow Implementation

### 1. **Pre-Filter After PDF Upload**
- When both PDF and hierarchical CSV are loaded, a modal appears
- User can select their working area (e.g., Level 1 → Zone A)
- This filters all subsequent room suggestions

### 2. **Filtered Room Suggestions During Annotation**
- When drawing boxes/polygons, room dropdown only shows pre-filtered rooms
- If user selected "Level 1 → Zone A", only rooms from that area appear
- Streamlines the annotation process

## Test Steps

### Step 1: Upload Files
1. Open http://localhost:5174
2. Upload a PDF file (any PDF)
3. Upload the hierarchical CSV (test_edge_cases.csv)
4. **Expected:** Pre-filter modal should appear automatically

### Step 2: Set Pre-Filter
1. In the pre-filter modal, select hierarchy levels:
   - First dropdown: Select "Building A"
   - Second dropdown: Select "Floor 1" 
   - Third dropdown: Select "Wing North"
2. Click "Apply Filter"
3. **Expected:** Modal closes, filter is active

### Step 3: Test Filtered Annotations
1. Draw a rectangle on the PDF
2. **Expected:** Room dropdown shows only rooms from "Building A → Floor 1 → Wing North"
3. Should see: "Room 101", "Room 102" (not rooms from other areas)

### Step 4: Test Skip Option
1. Refresh page, upload files again
2. In pre-filter modal, click "Skip - Show All Rooms"
3. Draw a rectangle
4. **Expected:** Room dropdown shows all rooms from CSV

## Key Features Implemented

### ✅ Pre-Filter Modal
- Appears automatically after PDF + hierarchical CSV upload
- Clean, centered modal with clear instructions
- Skip option for users who want all rooms

### ✅ Hierarchical Selection
- Same cascading dropdown interface
- Path selection without requiring specific room
- Visual feedback for selected path

### ✅ Filtered Room Suggestions
- RoomNameDropdown uses filtered room list
- Only shows rooms matching pre-selected hierarchy
- Maintains full room paths for annotation data

### ✅ User Experience
- Clear workflow: Filter first, then annotate
- Reduces cognitive load during annotation
- Maintains flexibility with skip option

## Technical Implementation

### Files Modified:
- `src/App.jsx`: Added pre-filter state and handlers
- `src/App.css`: Added pre-filter modal styles
- Room filtering logic integrated with existing system

### Key Functions:
```javascript
// Show pre-filter when both PDF and CSV loaded
useEffect(() => {
  if (pdfPages.length > 0 && csvStructure && csvStructure.maxDepth > 1) {
    setShowPreFilter(true)
  }
}, [pdfPages.length, csvStructure])

// Use filtered rooms in dropdown
<RoomNameDropdown
  roomNames={hierarchicalFilter.isFilterActive ? 
    hierarchicalFilter.getFilteredRoomNames() : roomNames}
/>
```

## Benefits

1. **Reduced Cognitive Load**: Users focus on one area at a time
2. **Faster Annotation**: Shorter room lists, quicker selection
3. **Better Organization**: Natural workflow matches building navigation
4. **Flexible**: Skip option for users who prefer seeing all rooms
5. **Consistent**: Same hierarchical interface throughout

## Test Data

Use `test_edge_cases.csv` which contains:
```
Building A,Floor 1,Wing North,Room 101
Building A,Floor 1,Wing North,Room 102
Building A,Floor 1,Wing South,Room 103
Building A,Floor 2,Wing North,Room 201
Building B,Floor 1,Wing East,Room 301
...
```

This allows testing the full hierarchy filtering workflow.
