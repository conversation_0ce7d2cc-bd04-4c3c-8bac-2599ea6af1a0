# Hand Tool and Canvas Panning Test Guide

## New Features Implemented

### 1. **Hand Tool (✋)**
**Purpose**: Allows users to drag and pan around the PDF canvas
**Activation**: Click hand tool button or press 'H' key
**Functionality**: 
- Drag to move the entire canvas around
- Useful for navigating large PDFs when zoomed in
- Cursor changes to grab/grabbing states

### 2. **Mouse Wheel Zoom Only**
**Change**: Mouse wheel now only controls zoom, not scrolling
**Behavior**: 
- Scroll up = Zoom in
- Scroll down = Zoom out
- No page scrolling when using mouse wheel on canvas

### 3. **Canvas Panning System**
**Implementation**: 
- Canvas position controlled by `canvasOffset` state
- Transform applied: `translate(x, y) scale(zoom)`
- Smooth transitions with CSS

## Test Scenarios

### Test 1: Hand Tool Activation
**Setup**: Upload a PDF file

**Hand Tool Selection**:
- [ ] Click ✋ button - tool becomes active (blue background)
- [ ] Press 'H' key - switches to hand tool
- [ ] Cursor changes to grab cursor when over canvas
- [ ] Other tools become inactive when hand tool is selected

**Visual Feedback**:
- [ ] Hand tool button shows active state (blue background)
- [ ] Canvas cursor changes to grab (open hand)
- [ ] Canvas cursor changes to grabbing (closed hand) when dragging

### Test 2: Canvas Panning Functionality
**Setup**: Select hand tool and zoom in to 200%+ for better testing

**Basic Panning**:
- [ ] Click and drag on canvas - PDF moves in drag direction
- [ ] Release mouse - panning stops, cursor returns to grab
- [ ] Pan in all directions (up, down, left, right, diagonal)
- [ ] Panning is smooth without jerky movements

**Panning Behavior**:
- [ ] PDF can be dragged beyond viewport boundaries
- [ ] Annotations move with the PDF during panning
- [ ] Zoom level is maintained during panning
- [ ] Multiple pan operations work consecutively

### Test 3: Mouse Wheel Zoom Integration
**Setup**: Use hand tool with various zoom levels

**Zoom + Pan Combination**:
- [ ] Mouse wheel zooms in/out while in hand mode
- [ ] Zoom maintains current pan position
- [ ] Can pan after zooming without switching tools
- [ ] Zoom indicator appears during wheel zoom
- [ ] Pan position is preserved across zoom changes

**No Scrolling Behavior**:
- [ ] Mouse wheel does NOT scroll the page
- [ ] Mouse wheel does NOT move the canvas position
- [ ] Only zoom functionality is triggered by wheel
- [ ] Browser page scrolling is prevented

### Test 4: Tool Switching
**Setup**: Test switching between different tools

**From Hand Tool**:
- [ ] Switch to Rectangle (R key or button) - hand mode deactivates
- [ ] Switch to Polygon (P key or button) - hand mode deactivates
- [ ] Cursor changes appropriately when switching tools
- [ ] Pan position is maintained when switching tools

**To Hand Tool**:
- [ ] Switch from Rectangle to Hand (H key) - works correctly
- [ ] Switch from Polygon to Hand (H key) - works correctly
- [ ] Current drawing operations are cancelled when switching to hand
- [ ] Selected annotations remain selected when switching to hand

### Test 5: Reset Position Functionality
**Setup**: Pan the canvas to various positions

**Reset Button (🎯)**:
- [ ] Click 🎯 button - canvas returns to center position
- [ ] Zoom level is maintained during reset
- [ ] Annotations return to original positions
- [ ] Reset works from any pan position

**Auto-fit Integration**:
- [ ] Click 📐 (auto-fit) button - resets both zoom and position
- [ ] PDF centers and scales to optimal size
- [ ] Pan position is reset to (0, 0)

### Test 6: Annotation Interaction with Panning
**Setup**: Create annotations, then use hand tool

**Annotation Visibility**:
- [ ] Annotations move correctly with canvas during panning
- [ ] Annotation positions remain accurate relative to PDF
- [ ] Selected annotations maintain selection during panning
- [ ] Annotation handles move with annotations during panning

**Annotation Creation After Panning**:
- [ ] Switch to Rectangle tool after panning - annotations create at correct positions
- [ ] Switch to Polygon tool after panning - annotations create at correct positions
- [ ] Coordinate system remains accurate regardless of pan position

### Test 7: Performance Testing
**Setup**: Test with large PDFs and many annotations

**Smooth Performance**:
- [ ] Panning is smooth without lag or stuttering
- [ ] No frame drops during continuous panning
- [ ] Memory usage remains stable during extended panning
- [ ] Canvas redraws efficiently during pan operations

**Large Document Handling**:
- [ ] Panning works well with large PDF files
- [ ] Performance remains good with many annotations
- [ ] Zoom + pan combination remains smooth

### Test 8: Keyboard Shortcuts
**Setup**: Test all keyboard shortcuts

**Tool Switching Shortcuts**:
- [ ] 'H' key switches to Hand tool
- [ ] 'R' key switches to Rectangle tool  
- [ ] 'P' key switches to Polygon tool
- [ ] Shortcuts work regardless of current tool
- [ ] Both uppercase and lowercase keys work

**Escape Key Behavior**:
- [ ] Escape cancels current drawing operations
- [ ] Escape stops panning if in progress
- [ ] Escape clears selected annotations
- [ ] Escape works from any tool mode

### Test 9: Edge Cases
**Setup**: Test unusual scenarios

**Extreme Panning**:
- [ ] Pan very far in one direction - still works correctly
- [ ] Pan to extreme positions - reset button still works
- [ ] Pan with very high zoom levels - remains smooth
- [ ] Pan with very low zoom levels - works correctly

**Rapid Operations**:
- [ ] Rapid tool switching - no state corruption
- [ ] Rapid pan + zoom operations - remains stable
- [ ] Quick pan movements - no visual artifacts

## Expected Visual Behavior

### Hand Tool Active State
```
Toolbar: Hand button (✋) has blue background
Canvas: Cursor shows grab (open hand) icon
Dragging: Cursor shows grabbing (closed hand) icon
```

### Canvas Transform
```
CSS Transform: translate(offsetX, offsetY) scale(zoom)
Transform Origin: center center
Transition: smooth 0.1s ease-out
```

### Reset States
```
Default Position: canvasOffset = {x: 0, y: 0}
After Reset: Canvas centered in viewport
After Auto-fit: Position reset + optimal zoom applied
```

## Success Criteria

The hand tool implementation is successful when:
- ✅ Hand tool provides intuitive canvas panning
- ✅ Mouse wheel only controls zoom, not scrolling
- ✅ Panning is smooth and responsive
- ✅ Tool switching works seamlessly
- ✅ Keyboard shortcuts function correctly
- ✅ Reset position works from any pan state
- ✅ Annotations maintain accuracy during panning
- ✅ Performance remains good during all operations
- ✅ Visual feedback is clear and immediate

## Common Issues to Watch For

### Panning Issues
- Jerky or laggy pan movements
- Canvas jumping to wrong positions
- Pan not working in certain directions
- Cursor not changing appropriately

### Zoom Integration Issues
- Mouse wheel causing page scroll instead of zoom
- Zoom not maintaining pan position
- Conflicts between zoom and pan operations

### Tool Switching Issues
- Tools not deactivating properly
- State corruption when switching rapidly
- Keyboard shortcuts not working
- Visual states not updating

### Performance Issues
- Lag during panning operations
- Memory leaks from continuous panning
- Slow canvas redraws
- Browser freezing during rapid operations

## Technical Implementation Notes

### State Management
```javascript
const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 })
const [isPanning, setIsPanning] = useState(false)
const [panStart, setPanStart] = useState({ x: 0, y: 0 })
```

### Mouse Event Handling
```javascript
// Pan calculation
const deltaX = event.clientX - panStart.x
const deltaY = event.clientY - panStart.y
setCanvasOffset(prev => ({
  x: prev.x + deltaX,
  y: prev.y + deltaY
}))
```

### CSS Cursor States
```css
.pdf-canvas.hand-cursor { cursor: grab; }
.pdf-canvas.hand-cursor:active { cursor: grabbing; }
```

This implementation provides professional-grade canvas navigation that users expect in modern PDF annotation applications.
